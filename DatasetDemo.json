{"labels": ["label1", "label2"], "splits": {"train_videos": [["train_vid_1", "train_vid_2"]], "test_videos": [["test_vid_1", "test_vid_2"]]}, "videos_metadata": {"video_01": {"video_path": "/path/to/video_01.mp4", "audio_path": "/path/to/video_01.wav", "nframes": 900, "resolution": [1920, 1080]}}, "gttubes": {"video_01": [{"instance_id": "vid01_ins01", "player_name": "<PERSON>", "player_id": "player_30", "jersey_color": "white", "label": "shoot_3pt", "tube": [[735, 810, 450, 890, 680], [736, 812, 452, 892, 682], [745, 820, 460, 900, 690]]}, {"instance_id": "vid01_ins02", "player_name": "<PERSON><PERSON>", "player_id": "player_11", "jersey_color": "white", "label": "catch_and_shoot", "tube": [[788, 320, 510, 400, 740]]}]}, "caption": {"vid01_ins01": "<PERSON> pulls up for a long three pointer.", "vid01_ins02": "<PERSON><PERSON> with a quick catch and shoot.", "vid01_ins03": "<PERSON> uses a dribble move to get past the defender."}, "Image_query": {"white_player_30": "image_path_1", "white_player_11": "image_path_2"}, "Audio_Transcripts": {"video_01": [{"t_start": 0.0, "t_end": 5.0, "transcript_text": "Sample transcript text"}]}}